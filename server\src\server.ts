import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { config } from './config';
import { authMiddleware } from './middleware/auth';
import { errorHandler } from './middleware/error-handler';
import { rateLimiter } from './middleware/rate-limiter';
import { WebSocketService } from './services/websocket.service';
import routes from './routes';
import { logger } from './utils/logger';

class WatchtowerServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private wsService: WebSocketService;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: config.cors.origin,
        methods: ['GET', 'POST']
      }
    });
    this.wsService = new WebSocketService(this.io);
    
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    this.app.use(helmet());
    this.app.use(cors(config.cors));
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    this.app.use(rateLimiter);
  }

  private initializeRoutes(): void {
    this.app.use('/api/auth', routes.auth);
    this.app.use('/api/devices', authMiddleware, routes.devices);
    this.app.use('/api/children', authMiddleware, routes.children);
    this.app.use('/api/policies', authMiddleware, routes.policies);
    this.app.use('/api/analytics', authMiddleware, routes.analytics);
    this.app.use('/api/alerts', authMiddleware, routes.alerts);
  }

  private initializeErrorHandling(): void {
    this.app.use(errorHandler);
  }

  public start(): void {
    this.server.listen(config.server.port, () => {
      logger.info(`🚀 Watchtower Server running on port ${config.server.port}`);
      logger.info(`📊 Dashboard available at http://localhost:${config.server.port}/dashboard`);
      logger.info(`🔗 API documentation at http://localhost:${config.server.port}/api/docs`);
    });
  }
}

const server = new WatchtowerServer();
server.start();
