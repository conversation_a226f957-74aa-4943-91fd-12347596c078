# Watchtower - Parental Monitoring System

A comprehensive parental monitoring solution with both self-hosted and cloud-based deployment options.

## Features

- Real-time network traffic monitoring
- Content filtering and blocking
- Multi-device support (Windows, macOS, Linux, iOS, Android)
- Web-based dashboard for parents
- Advanced analytics and reporting
- Privacy-focused design with end-to-end encryption

## Quick Start

### Development Setup

1. Clone the repository
2. Run the setup script: `./scripts/setup-dev.sh`
3. Start development servers: `npm run dev`

### Production Deployment

- **Self-hosted**: See [deployment guide](docs/deployment-guide.md)
- **Cloud**: Contact support for SaaS setup

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Web Dashboard                            │
└─────────────────────┬───────────────────────────────────────────┘
                      │ HTTPS/WebSocket
┌─────────────────────┴───────────────────────────────────────────┐
│                 Watchtower Server                               │
└─────────────────────┬───────────────────────────────────────────┘
                      │ Encrypted Communication
┌─────────────────────┴───────────────────────────────────────────┐
│                  Watchtower Agents                              │
└─────────────────────────────────────────────────────────────────┘
```

## License

Proprietary - All rights reserved
