{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "WatchtowerAgent": {"ServerUrl": "https://localhost:3000", "DeviceName": "", "ApiKey": "", "Monitoring": {"EnableScreenCapture": true, "EnableKeylogger": false, "EnableApplicationMonitoring": true, "EnableNetworkMonitoring": true, "EnableFileSystemMonitoring": true, "ScreenCaptureInterval": 30, "EventBatchSize": 50, "EventBatchInterval": 60}, "ContentFilter": {"EnableContentFiltering": true, "BlockedKeywords": ["inappropriate", "violence", "adult"], "BlockedDomains": ["example-blocked-site.com"], "AllowedDomains": ["educational-site.com", "school-website.edu"], "BlockAdultContent": true, "BlockViolentContent": true, "RequireApprovalForNewSites": false, "ContentScanSensitivity": 5}, "Applications": {"BlockedApplications": ["game.exe", "inappropriate-app.exe"], "AllowedApplications": ["notepad.exe", "calculator.exe", "educational-software.exe"], "BlockUnknownApplications": false, "RequireApprovalForNewApps": true, "AlwaysAllowedApps": ["explorer.exe", "winlogon.exe", "csrss.exe"], "BlockGamesDuringSchoolHours": true}, "Network": {"EnableNetworkFiltering": true, "BlockedIPs": [], "BlockedPorts": [6881, 6882, 6883], "BlockP2PTraffic": true, "LogAllNetworkActivity": false, "MaxDownloadSpeed": 0, "MaxUploadSpeed": 0}, "Schedule": {"EnableScheduleRestrictions": true, "TimeRestrictions": [{"DayOfWeek": 1, "StartTime": "09:00:00", "EndTime": "15:00:00", "IsBlocked": false}, {"DayOfWeek": 2, "StartTime": "09:00:00", "EndTime": "15:00:00", "IsBlocked": false}, {"DayOfWeek": 3, "StartTime": "09:00:00", "EndTime": "15:00:00", "IsBlocked": false}, {"DayOfWeek": 4, "StartTime": "09:00:00", "EndTime": "15:00:00", "IsBlocked": false}, {"DayOfWeek": 5, "StartTime": "09:00:00", "EndTime": "15:00:00", "IsBlocked": false}], "MaxDailyUsage": 0, "BlockDuringBedtime": true, "BedtimeStart": "22:00:00", "BedtimeEnd": "07:00:00"}, "Security": {"EnableTamperProtection": true, "DisableTaskManager": true, "DisableRegistryEditor": true, "DisableCommandPrompt": true, "ParentPasswordRequired": true}, "Local": {"LogLevel": "Information", "EnableDebugMode": false, "MaxLogFileSizeMB": 100, "LogRetentionDays": 30}}}