using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Timers;
using Tesseract;
using WatchtowerAgent.Config;
using WatchtowerAgent.Models;
using Timer = System.Timers.Timer;

namespace WatchtowerAgent.Services
{
    public class ContentScanner : IDisposable
    {
        private readonly ILogger<ContentScanner> _logger;
        private readonly AgentConfiguration _config;
        private readonly ApiClient _apiClient;
        private readonly Timer _captureTimer;
        private readonly List<MonitoringEvent> _eventQueue;
        private TesseractEngine? _ocrEngine;
        private bool _isRunning = false;
        private bool _disposed = false;

        // Windows API imports for screen capture
        [DllImport("user32.dll")]
        private static extern IntPtr GetDesktopWindow();

        [DllImport("user32.dll")]
        private static extern IntPtr GetWindowDC(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern IntPtr ReleaseDC(IntPtr hWnd, IntPtr hDC);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleDC(IntPtr hDC);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleBitmap(IntPtr hDC, int nWidth, int nHeight);

        [DllImport("gdi32.dll")]
        private static extern IntPtr SelectObject(IntPtr hDC, IntPtr hGDIObj);

        [DllImport("gdi32.dll")]
        private static extern bool BitBlt(IntPtr hDestDC, int x, int y, int nWidth, int nHeight,
            IntPtr hSrcDC, int xSrc, int ySrc, int dwRop);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteDC(IntPtr hDC);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);

        [DllImport("user32.dll")]
        private static extern int GetSystemMetrics(int nIndex);

        private const int SM_CXSCREEN = 0;
        private const int SM_CYSCREEN = 1;
        private const int SRCCOPY = 0x00CC0020;

        public ContentScanner(ILogger<ContentScanner> logger, AgentConfiguration config, ApiClient apiClient)
        {
            _logger = logger;
            _config = config;
            _apiClient = apiClient;
            _eventQueue = new List<MonitoringEvent>();
            
            // Initialize capture timer based on configuration
            var interval = _config.ServerConfig.Monitoring.ScreenCaptureInterval * 1000;
            _captureTimer = new Timer(interval);
            _captureTimer.Elapsed += OnCaptureTimerElapsed;
        }

        public async Task StartAsync()
        {
            if (_isRunning) return;

            _logger.LogInformation("Starting content scanner...");

            try
            {
                // Initialize OCR engine
                await InitializeOcrEngineAsync();
                
                // Start screen capture timer
                if (_config.IsMonitoringEnabled("screen"))
                {
                    _captureTimer.Start();
                }
                
                _isRunning = true;
                _logger.LogInformation("Content scanner started successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start content scanner");
                throw;
            }
        }

        public async Task StopAsync()
        {
            if (!_isRunning) return;

            _logger.LogInformation("Stopping content scanner...");

            _captureTimer.Stop();
            
            // Send any remaining events
            await FlushEventQueueAsync();
            
            _isRunning = false;
            _logger.LogInformation("Content scanner stopped");
        }

        private async Task InitializeOcrEngineAsync()
        {
            try
            {
                // Initialize Tesseract OCR engine
                var tessDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "tessdata");
                if (Directory.Exists(tessDataPath))
                {
                    _ocrEngine = new TesseractEngine(tessDataPath, "eng", EngineMode.Default);
                    _logger.LogInformation("OCR engine initialized successfully");
                }
                else
                {
                    _logger.LogWarning("Tesseract data not found, OCR functionality will be disabled");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize OCR engine");
            }
        }

        private async void OnCaptureTimerElapsed(object sender, ElapsedEventArgs e)
        {
            if (!_config.IsMonitoringEnabled("screen")) return;

            try
            {
                await CaptureAndAnalyzeScreenAsync();
                await FlushEventQueueAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during screen capture and analysis");
            }
        }

        private async Task CaptureAndAnalyzeScreenAsync()
        {
            try
            {
                // Capture screenshot
                var screenshot = CaptureScreen();
                if (screenshot == null) return;

                // Convert to byte array
                var imageData = BitmapToByteArray(screenshot);
                
                // Perform OCR to extract text
                var extractedText = await ExtractTextFromImageAsync(screenshot);
                
                // Analyze content for inappropriate material
                var analysisResult = await AnalyzeContentAsync(extractedText, imageData);
                
                // Create screen capture data
                var captureData = new ScreenCaptureData
                {
                    DeviceId = _config.DeviceId,
                    ImageData = imageData,
                    Format = "PNG",
                    Width = screenshot.Width,
                    Height = screenshot.Height,
                    DetectedText = extractedText,
                    ContainsInappropriateContent = analysisResult.IsInappropriate
                };

                // Create monitoring event
                var monitoringEvent = new MonitoringEvent
                {
                    DeviceId = _config.DeviceId,
                    EventType = "ScreenCapture",
                    Category = "Content",
                    Description = analysisResult.IsInappropriate ? 
                        "Inappropriate content detected on screen" : 
                        "Screen content captured",
                    Data = new Dictionary<string, object>
                    {
                        ["Width"] = screenshot.Width,
                        ["Height"] = screenshot.Height,
                        ["TextLength"] = extractedText?.Length ?? 0,
                        ["InappropriateContent"] = analysisResult.IsInappropriate,
                        ["DetectedKeywords"] = analysisResult.DetectedKeywords,
                        ["ConfidenceScore"] = analysisResult.ConfidenceScore
                    },
                    Severity = analysisResult.IsInappropriate ? "Warning" : "Info",
                    RequiresParentNotification = analysisResult.IsInappropriate,
                    WasBlocked = false
                };

                _eventQueue.Add(monitoringEvent);

                // If inappropriate content is detected, take action
                if (analysisResult.IsInappropriate)
                {
                    await HandleInappropriateContentAsync(analysisResult, captureData);
                }

                screenshot.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error capturing and analyzing screen");
            }
        }

        private Bitmap? CaptureScreen()
        {
            try
            {
                var screenWidth = GetSystemMetrics(SM_CXSCREEN);
                var screenHeight = GetSystemMetrics(SM_CYSCREEN);

                var hDesktop = GetDesktopWindow();
                var hSrcDC = GetWindowDC(hDesktop);
                var hDestDC = CreateCompatibleDC(hSrcDC);
                var hBitmap = CreateCompatibleBitmap(hSrcDC, screenWidth, screenHeight);
                var hOld = SelectObject(hDestDC, hBitmap);

                BitBlt(hDestDC, 0, 0, screenWidth, screenHeight, hSrcDC, 0, 0, SRCCOPY);

                SelectObject(hDestDC, hOld);
                DeleteDC(hDestDC);
                ReleaseDC(hDesktop, hSrcDC);

                var bitmap = Image.FromHbitmap(hBitmap);
                DeleteObject(hBitmap);

                return bitmap;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to capture screen");
                return null;
            }
        }

        private byte[] BitmapToByteArray(Bitmap bitmap)
        {
            using var stream = new MemoryStream();
            bitmap.Save(stream, ImageFormat.Png);
            return stream.ToArray();
        }

        private async Task<string?> ExtractTextFromImageAsync(Bitmap image)
        {
            if (_ocrEngine == null) return null;

            try
            {
                using var pix = PixConverter.ToPix(image);
                using var page = _ocrEngine.Process(pix);
                return page.GetText();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract text from image");
                return null;
            }
        }

        private async Task<ContentAnalysisResult> AnalyzeContentAsync(string? text, byte[] imageData)
        {
            var result = new ContentAnalysisResult();

            try
            {
                // Analyze text for inappropriate keywords
                if (!string.IsNullOrEmpty(text))
                {
                    result = AnalyzeTextContent(text);
                }

                // TODO: Add image analysis for inappropriate visual content
                // This would typically involve calling an AI service like Azure Cognitive Services
                // or Google Vision API to detect adult content, violence, etc.

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error analyzing content");
                return result;
            }
        }

        private ContentAnalysisResult AnalyzeTextContent(string text)
        {
            var result = new ContentAnalysisResult();
            var blockedKeywords = _config.ServerConfig.ContentFilter.BlockedKeywords;
            var detectedKeywords = new List<string>();

            foreach (var keyword in blockedKeywords)
            {
                if (text.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    detectedKeywords.Add(keyword);
                }
            }

            result.DetectedKeywords = detectedKeywords;
            result.IsInappropriate = detectedKeywords.Count > 0;
            result.ConfidenceScore = detectedKeywords.Count > 0 ? 0.8f : 0.1f;

            return result;
        }

        private async Task HandleInappropriateContentAsync(ContentAnalysisResult analysis, ScreenCaptureData captureData)
        {
            try
            {
                // Log the incident
                _logger.LogWarning($"Inappropriate content detected: {string.Join(", ", analysis.DetectedKeywords)}");

                // Send immediate alert to server
                var alertEvent = new MonitoringEvent
                {
                    DeviceId = _config.DeviceId,
                    EventType = "InappropriateContentAlert",
                    Category = "Security",
                    Description = $"Inappropriate content detected: {string.Join(", ", analysis.DetectedKeywords)}",
                    Data = new Dictionary<string, object>
                    {
                        ["DetectedKeywords"] = analysis.DetectedKeywords,
                        ["ConfidenceScore"] = analysis.ConfidenceScore,
                        ["ScreenshotIncluded"] = true
                    },
                    Severity = "Critical",
                    RequiresParentNotification = true,
                    WasBlocked = false
                };

                await _apiClient.SendEventAsync(alertEvent);

                // TODO: Implement content blocking if configured
                // This could involve overlaying the screen, minimizing windows, etc.
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling inappropriate content");
            }
        }

        private async Task FlushEventQueueAsync()
        {
            if (_eventQueue.Count == 0) return;

            try
            {
                var events = _eventQueue.ToList();
                _eventQueue.Clear();
                
                await _apiClient.SendBulkEventsAsync(events);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send content scanner events to server");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _captureTimer?.Dispose();
                _ocrEngine?.Dispose();
                _disposed = true;
            }
        }
    }

    public class ContentAnalysisResult
    {
        public bool IsInappropriate { get; set; } = false;
        public List<string> DetectedKeywords { get; set; } = new();
        public float ConfidenceScore { get; set; } = 0.0f;
        public List<string> DetectedObjects { get; set; } = new();
    }
}
