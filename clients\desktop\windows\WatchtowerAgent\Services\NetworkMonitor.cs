using Microsoft.Extensions.Logging;
using PacketDotNet;
using SharpPcap;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Timers;
using WatchtowerAgent.Config;
using WatchtowerAgent.Models;
using Timer = System.Timers.Timer;

namespace WatchtowerAgent.Services
{
    public class NetworkMonitor : IDisposable
    {
        private readonly ILogger<NetworkMonitor> _logger;
        private readonly AgentConfiguration _config;
        private readonly ApiClient _apiClient;
        private readonly Timer _statsTimer;
        private readonly List<MonitoringEvent> _eventQueue;
        private readonly Dictionary<string, NetworkConnection> _activeConnections;
        private ICaptureDevice? _captureDevice;
        private bool _isRunning = false;
        private bool _disposed = false;

        public NetworkMonitor(ILogger<NetworkMonitor> logger, AgentConfiguration config, ApiClient apiClient)
        {
            _logger = logger;
            _config = config;
            _apiClient = apiClient;
            _eventQueue = new List<MonitoringEvent>();
            _activeConnections = new Dictionary<string, NetworkConnection>();
            
            _statsTimer = new Timer(30000); // Send stats every 30 seconds
            _statsTimer.Elapsed += OnStatsTimerElapsed;
        }

        public async Task StartAsync()
        {
            if (_isRunning) return;

            _logger.LogInformation("Starting network monitor...");

            try
            {
                // Initialize packet capture
                await InitializePacketCaptureAsync();
                
                // Start monitoring active connections
                await StartConnectionMonitoringAsync();
                
                // Start statistics timer
                _statsTimer.Start();
                
                _isRunning = true;
                _logger.LogInformation("Network monitor started successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start network monitor");
                throw;
            }
        }

        public async Task StopAsync()
        {
            if (!_isRunning) return;

            _logger.LogInformation("Stopping network monitor...");

            _statsTimer.Stop();
            
            if (_captureDevice != null)
            {
                _captureDevice.StopCapture();
                _captureDevice.Close();
            }
            
            // Send any remaining events
            await FlushEventQueueAsync();
            
            _isRunning = false;
            _logger.LogInformation("Network monitor stopped");
        }

        private async Task InitializePacketCaptureAsync()
        {
            try
            {
                var devices = CaptureDeviceList.Instance;
                if (devices.Count == 0)
                {
                    _logger.LogWarning("No network devices found for packet capture");
                    return;
                }

                // Find the first active network interface
                foreach (var device in devices)
                {
                    try
                    {
                        device.Open(DeviceModes.Promiscuous, 1000);
                        
                        // Set up packet handler
                        device.OnPacketArrival += OnPacketArrival;
                        
                        // Start capture
                        device.StartCapture();
                        
                        _captureDevice = device;
                        _logger.LogInformation($"Started packet capture on device: {device.Description}");
                        break;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"Failed to open device: {device.Description}");
                        device.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize packet capture");
            }
        }

        private async Task StartConnectionMonitoringAsync()
        {
            // Start monitoring network connections using netstat-like functionality
            _ = Task.Run(async () =>
            {
                while (_isRunning)
                {
                    try
                    {
                        await MonitorActiveConnectionsAsync();
                        await Task.Delay(5000); // Check every 5 seconds
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error monitoring network connections");
                    }
                }
            });
        }

        private void OnPacketArrival(object sender, PacketCapture e)
        {
            try
            {
                if (!_config.IsMonitoringEnabled("network")) return;

                var packet = Packet.ParsePacket(e.GetPacket().LinkLayerType, e.GetPacket().Data);
                var ipPacket = packet.Extract<IPPacket>();
                
                if (ipPacket != null)
                {
                    ProcessIpPacket(ipPacket);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error processing packet");
            }
        }

        private void ProcessIpPacket(IPPacket ipPacket)
        {
            try
            {
                var tcpPacket = ipPacket.Extract<TcpPacket>();
                var udpPacket = ipPacket.Extract<UdpPacket>();

                if (tcpPacket != null)
                {
                    ProcessTcpPacket(ipPacket, tcpPacket);
                }
                else if (udpPacket != null)
                {
                    ProcessUdpPacket(ipPacket, udpPacket);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error processing IP packet");
            }
        }

        private void ProcessTcpPacket(IPPacket ipPacket, TcpPacket tcpPacket)
        {
            var sourceIP = ipPacket.SourceAddress.ToString();
            var destIP = ipPacket.DestinationAddress.ToString();
            var sourcePort = tcpPacket.SourcePort;
            var destPort = tcpPacket.DestinationPort;

            // Check if this connection should be blocked
            if (ShouldBlockConnection(destIP, destPort))
            {
                LogBlockedConnection(sourceIP, destIP, sourcePort, destPort, "TCP");
                return;
            }

            // Log the connection
            var connectionKey = $"{sourceIP}:{sourcePort}-{destIP}:{destPort}";
            if (!_activeConnections.ContainsKey(connectionKey))
            {
                var connection = new NetworkConnection
                {
                    SourceIP = sourceIP,
                    DestinationIP = destIP,
                    SourcePort = sourcePort,
                    DestinationPort = destPort,
                    Protocol = "TCP",
                    StartTime = DateTime.UtcNow,
                    BytesSent = 0,
                    BytesReceived = 0
                };

                _activeConnections[connectionKey] = connection;
                LogNewConnection(connection);
            }

            // Update byte counts
            if (_activeConnections.TryGetValue(connectionKey, out var existingConnection))
            {
                existingConnection.BytesSent += (long)ipPacket.TotalLength;
            }
        }

        private void ProcessUdpPacket(IPPacket ipPacket, UdpPacket udpPacket)
        {
            var sourceIP = ipPacket.SourceAddress.ToString();
            var destIP = ipPacket.DestinationAddress.ToString();
            var sourcePort = udpPacket.SourcePort;
            var destPort = udpPacket.DestinationPort;

            // Check if this connection should be blocked
            if (ShouldBlockConnection(destIP, destPort))
            {
                LogBlockedConnection(sourceIP, destIP, sourcePort, destPort, "UDP");
                return;
            }

            // Log UDP traffic (typically connectionless)
            var networkEvent = new NetworkEvent
            {
                Protocol = "UDP",
                SourceIP = sourceIP,
                DestinationIP = destIP,
                SourcePort = sourcePort,
                DestinationPort = destPort,
                BytesSent = ipPacket.TotalLength,
                Action = "Allowed"
            };

            LogNetworkEvent(networkEvent);
        }

        private bool ShouldBlockConnection(string destIP, int destPort)
        {
            // Check blocked IPs
            if (_config.ServerConfig.Network.BlockedIPs.Contains(destIP))
            {
                return true;
            }

            // Check blocked ports
            if (_config.ServerConfig.Network.BlockedPorts.Contains(destPort))
            {
                return true;
            }

            // Check if it's P2P traffic (common P2P ports)
            if (_config.ServerConfig.Network.BlockP2PTraffic)
            {
                var p2pPorts = new[] { 6881, 6882, 6883, 6884, 6885, 6886, 6887, 6888, 6889 }; // BitTorrent
                if (p2pPorts.Contains(destPort))
                {
                    return true;
                }
            }

            return false;
        }

        private void LogNewConnection(NetworkConnection connection)
        {
            var monitoringEvent = new MonitoringEvent
            {
                DeviceId = _config.DeviceId,
                EventType = "NetworkConnectionEstablished",
                Category = "Network",
                Description = $"New {connection.Protocol} connection to {connection.DestinationIP}:{connection.DestinationPort}",
                Data = new Dictionary<string, object>
                {
                    ["Protocol"] = connection.Protocol,
                    ["SourceIP"] = connection.SourceIP,
                    ["DestinationIP"] = connection.DestinationIP,
                    ["SourcePort"] = connection.SourcePort,
                    ["DestinationPort"] = connection.DestinationPort,
                    ["Domain"] = ResolveDomainName(connection.DestinationIP)
                },
                Severity = "Info"
            };

            _eventQueue.Add(monitoringEvent);
        }

        private void LogBlockedConnection(string sourceIP, string destIP, int sourcePort, int destPort, string protocol)
        {
            var monitoringEvent = new MonitoringEvent
            {
                DeviceId = _config.DeviceId,
                EventType = "NetworkConnectionBlocked",
                Category = "Security",
                Description = $"Blocked {protocol} connection to {destIP}:{destPort}",
                Data = new Dictionary<string, object>
                {
                    ["Protocol"] = protocol,
                    ["SourceIP"] = sourceIP,
                    ["DestinationIP"] = destIP,
                    ["SourcePort"] = sourcePort,
                    ["DestinationPort"] = destPort,
                    ["Domain"] = ResolveDomainName(destIP)
                },
                Severity = "Warning",
                RequiresParentNotification = true,
                WasBlocked = true
            };

            _eventQueue.Add(monitoringEvent);
        }

        private void LogNetworkEvent(NetworkEvent networkEvent)
        {
            var monitoringEvent = new MonitoringEvent
            {
                DeviceId = _config.DeviceId,
                EventType = "NetworkTraffic",
                Category = "Network",
                Description = $"{networkEvent.Protocol} traffic to {networkEvent.DestinationIP}:{networkEvent.DestinationPort}",
                Data = new Dictionary<string, object>
                {
                    ["Protocol"] = networkEvent.Protocol,
                    ["SourceIP"] = networkEvent.SourceIP,
                    ["DestinationIP"] = networkEvent.DestinationIP,
                    ["SourcePort"] = networkEvent.SourcePort,
                    ["DestinationPort"] = networkEvent.DestinationPort,
                    ["BytesSent"] = networkEvent.BytesSent,
                    ["Action"] = networkEvent.Action
                },
                Severity = "Info"
            };

            _eventQueue.Add(monitoringEvent);
        }

        private async Task MonitorActiveConnectionsAsync()
        {
            try
            {
                // Get current TCP connections
                var tcpConnections = IPGlobalProperties.GetIPGlobalProperties().GetActiveTcpConnections();
                
                foreach (var connection in tcpConnections)
                {
                    if (connection.State == TcpState.Established)
                    {
                        var key = $"{connection.LocalEndPoint}-{connection.RemoteEndPoint}";
                        
                        if (!_activeConnections.ContainsKey(key))
                        {
                            var networkConnection = new NetworkConnection
                            {
                                SourceIP = connection.LocalEndPoint.Address.ToString(),
                                DestinationIP = connection.RemoteEndPoint.Address.ToString(),
                                SourcePort = connection.LocalEndPoint.Port,
                                DestinationPort = connection.RemoteEndPoint.Port,
                                Protocol = "TCP",
                                StartTime = DateTime.UtcNow
                            };

                            _activeConnections[key] = networkConnection;
                            LogNewConnection(networkConnection);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error monitoring active connections");
            }
        }

        private string ResolveDomainName(string ipAddress)
        {
            try
            {
                var hostEntry = Dns.GetHostEntry(ipAddress);
                return hostEntry.HostName;
            }
            catch
            {
                return ipAddress; // Return IP if DNS resolution fails
            }
        }

        private async void OnStatsTimerElapsed(object sender, ElapsedEventArgs e)
        {
            try
            {
                await FlushEventQueueAsync();
                await SendNetworkStatisticsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error sending network statistics");
            }
        }

        private async Task SendNetworkStatisticsAsync()
        {
            try
            {
                var stats = new
                {
                    ActiveConnections = _activeConnections.Count,
                    TotalBytesSent = _activeConnections.Values.Sum(c => c.BytesSent),
                    TotalBytesReceived = _activeConnections.Values.Sum(c => c.BytesReceived),
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                var statsEvent = new MonitoringEvent
                {
                    DeviceId = _config.DeviceId,
                    EventType = "NetworkStatistics",
                    Category = "Network",
                    Description = "Network usage statistics",
                    Data = new Dictionary<string, object>
                    {
                        ["ActiveConnections"] = stats.ActiveConnections,
                        ["TotalBytesSent"] = stats.TotalBytesSent,
                        ["TotalBytesReceived"] = stats.TotalBytesReceived
                    },
                    Severity = "Info"
                };

                await _apiClient.SendEventAsync(statsEvent);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send network statistics");
            }
        }

        private async Task FlushEventQueueAsync()
        {
            if (_eventQueue.Count == 0) return;

            try
            {
                var events = _eventQueue.ToList();
                _eventQueue.Clear();
                
                await _apiClient.SendBulkEventsAsync(events);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send network events to server");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _captureDevice?.Close();
                _statsTimer?.Dispose();
                _disposed = true;
            }
        }
    }

    public class NetworkConnection
    {
        public string SourceIP { get; set; } = string.Empty;
        public string DestinationIP { get; set; } = string.Empty;
        public int SourcePort { get; set; }
        public int DestinationPort { get; set; }
        public string Protocol { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public long BytesSent { get; set; }
        public long BytesReceived { get; set; }
    }
}
