using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;
using WatchtowerAgent.Config;
using WatchtowerAgent.Services;

namespace WatchtowerAgent.Forms
{
    public partial class ParentControlForm : Form
    {
        private readonly ILogger<ParentControlForm> _logger;
        private readonly AgentConfiguration _config;
        private readonly SecurityService _securityService;
        private readonly ApiClient _apiClient;
        
        private TextBox _passwordTextBox;
        private Button _loginButton;
        private Panel _loginPanel;
        private Panel _controlPanel;
        private Button _disableMonitoringButton;
        private Button _enableMonitoringButton;
        private Button _viewLogsButton;
        private Button _settingsButton;
        private Button _logoutButton;
        private Label _statusLabel;
        private ListBox _recentAlertsListBox;
        private CheckBox _screenMonitoringCheckBox;
        private CheckBox _appMonitoringCheckBox;
        private CheckBox _networkMonitoringCheckBox;
        private NumericUpDown _captureIntervalNumeric;

        public ParentControlForm(
            ILogger<ParentControlForm> logger,
            AgentConfiguration config,
            SecurityService securityService,
            ApiClient apiClient)
        {
            _logger = logger;
            _config = config;
            _securityService = securityService;
            _apiClient = apiClient;
            
            InitializeComponent();
            SetupEventHandlers();
            UpdateUI();
        }

        private void InitializeComponent()
        {
            this.Text = "Watchtower Parent Control";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;
            this.TopMost = true;

            // Login Panel
            _loginPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.LightGray
            };

            var loginLabel = new Label
            {
                Text = "Enter Parent Password:",
                Location = new Point(50, 100),
                Size = new Size(200, 30),
                Font = new Font("Arial", 12, FontStyle.Bold)
            };

            _passwordTextBox = new TextBox
            {
                Location = new Point(50, 140),
                Size = new Size(300, 30),
                UseSystemPasswordChar = true,
                Font = new Font("Arial", 12)
            };

            _loginButton = new Button
            {
                Text = "Login",
                Location = new Point(50, 180),
                Size = new Size(100, 35),
                BackColor = Color.DodgerBlue,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };

            var cancelButton = new Button
            {
                Text = "Cancel",
                Location = new Point(160, 180),
                Size = new Size(100, 35),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            cancelButton.Click += (s, e) => this.Close();

            _loginPanel.Controls.AddRange(new Control[] { loginLabel, _passwordTextBox, _loginButton, cancelButton });

            // Control Panel
            _controlPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Visible = false
            };

            var titleLabel = new Label
            {
                Text = "Watchtower Parent Control Panel",
                Location = new Point(20, 20),
                Size = new Size(400, 30),
                Font = new Font("Arial", 16, FontStyle.Bold),
                ForeColor = Color.DarkBlue
            };

            _statusLabel = new Label
            {
                Text = "Status: Monitoring Active",
                Location = new Point(20, 60),
                Size = new Size(300, 25),
                Font = new Font("Arial", 10),
                ForeColor = Color.Green
            };

            // Monitoring Controls
            var monitoringGroupBox = new GroupBox
            {
                Text = "Monitoring Controls",
                Location = new Point(20, 100),
                Size = new Size(250, 150),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };

            _screenMonitoringCheckBox = new CheckBox
            {
                Text = "Screen Monitoring",
                Location = new Point(10, 25),
                Size = new Size(150, 25),
                Checked = true
            };

            _appMonitoringCheckBox = new CheckBox
            {
                Text = "Application Monitoring",
                Location = new Point(10, 50),
                Size = new Size(150, 25),
                Checked = true
            };

            _networkMonitoringCheckBox = new CheckBox
            {
                Text = "Network Monitoring",
                Location = new Point(10, 75),
                Size = new Size(150, 25),
                Checked = true
            };

            var captureIntervalLabel = new Label
            {
                Text = "Capture Interval (sec):",
                Location = new Point(10, 105),
                Size = new Size(120, 20)
            };

            _captureIntervalNumeric = new NumericUpDown
            {
                Location = new Point(130, 103),
                Size = new Size(60, 25),
                Minimum = 10,
                Maximum = 300,
                Value = 30
            };

            monitoringGroupBox.Controls.AddRange(new Control[] 
            { 
                _screenMonitoringCheckBox, 
                _appMonitoringCheckBox, 
                _networkMonitoringCheckBox,
                captureIntervalLabel,
                _captureIntervalNumeric
            });

            // Action Buttons
            _disableMonitoringButton = new Button
            {
                Text = "Temporarily Disable Monitoring",
                Location = new Point(300, 120),
                Size = new Size(200, 35),
                BackColor = Color.Orange,
                ForeColor = Color.White,
                Font = new Font("Arial", 9, FontStyle.Bold)
            };

            _enableMonitoringButton = new Button
            {
                Text = "Enable Monitoring",
                Location = new Point(300, 160),
                Size = new Size(200, 35),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Arial", 9, FontStyle.Bold),
                Visible = false
            };

            _viewLogsButton = new Button
            {
                Text = "View Activity Logs",
                Location = new Point(300, 200),
                Size = new Size(200, 35),
                BackColor = Color.DodgerBlue,
                ForeColor = Color.White,
                Font = new Font("Arial", 9, FontStyle.Bold)
            };

            _settingsButton = new Button
            {
                Text = "Advanced Settings",
                Location = new Point(300, 240),
                Size = new Size(200, 35),
                BackColor = Color.Purple,
                ForeColor = Color.White,
                Font = new Font("Arial", 9, FontStyle.Bold)
            };

            // Recent Alerts
            var alertsGroupBox = new GroupBox
            {
                Text = "Recent Alerts",
                Location = new Point(20, 270),
                Size = new Size(480, 150),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };

            _recentAlertsListBox = new ListBox
            {
                Location = new Point(10, 25),
                Size = new Size(460, 115),
                Font = new Font("Arial", 9)
            };

            alertsGroupBox.Controls.Add(_recentAlertsListBox);

            // Logout Button
            _logoutButton = new Button
            {
                Text = "Logout",
                Location = new Point(450, 430),
                Size = new Size(100, 30),
                BackColor = Color.Red,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };

            _controlPanel.Controls.AddRange(new Control[] 
            { 
                titleLabel, 
                _statusLabel, 
                monitoringGroupBox,
                _disableMonitoringButton,
                _enableMonitoringButton,
                _viewLogsButton,
                _settingsButton,
                alertsGroupBox,
                _logoutButton
            });

            this.Controls.AddRange(new Control[] { _loginPanel, _controlPanel });
        }

        private void SetupEventHandlers()
        {
            _loginButton.Click += OnLoginButtonClick;
            _logoutButton.Click += OnLogoutButtonClick;
            _disableMonitoringButton.Click += OnDisableMonitoringClick;
            _enableMonitoringButton.Click += OnEnableMonitoringClick;
            _viewLogsButton.Click += OnViewLogsClick;
            _settingsButton.Click += OnSettingsClick;
            
            _passwordTextBox.KeyPress += (s, e) =>
            {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    OnLoginButtonClick(s, e);
                }
            };

            // Monitor checkbox changes
            _screenMonitoringCheckBox.CheckedChanged += OnMonitoringSettingChanged;
            _appMonitoringCheckBox.CheckedChanged += OnMonitoringSettingChanged;
            _networkMonitoringCheckBox.CheckedChanged += OnMonitoringSettingChanged;
            _captureIntervalNumeric.ValueChanged += OnMonitoringSettingChanged;
        }

        private async void OnLoginButtonClick(object sender, EventArgs e)
        {
            try
            {
                var password = _passwordTextBox.Text;
                if (string.IsNullOrEmpty(password))
                {
                    MessageBox.Show("Please enter a password.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (_securityService.AuthenticateParent(password))
                {
                    _loginPanel.Visible = false;
                    _controlPanel.Visible = true;
                    await LoadRecentAlerts();
                    UpdateMonitoringStatus();
                }
                else
                {
                    MessageBox.Show("Invalid password. Access denied.", "Authentication Failed", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    _passwordTextBox.Clear();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during parent login");
                MessageBox.Show("An error occurred during login.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OnLogoutButtonClick(object sender, EventArgs e)
        {
            _securityService.DisableParentMode();
            _controlPanel.Visible = false;
            _loginPanel.Visible = true;
            _passwordTextBox.Clear();
        }

        private void OnDisableMonitoringClick(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "Are you sure you want to temporarily disable monitoring?\nThis will stop all monitoring activities until re-enabled.",
                "Confirm Disable Monitoring",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // TODO: Implement temporary monitoring disable
                _disableMonitoringButton.Visible = false;
                _enableMonitoringButton.Visible = true;
                _statusLabel.Text = "Status: Monitoring Disabled";
                _statusLabel.ForeColor = Color.Red;
            }
        }

        private void OnEnableMonitoringClick(object sender, EventArgs e)
        {
            // TODO: Implement monitoring re-enable
            _enableMonitoringButton.Visible = false;
            _disableMonitoringButton.Visible = true;
            _statusLabel.Text = "Status: Monitoring Active";
            _statusLabel.ForeColor = Color.Green;
        }

        private void OnViewLogsClick(object sender, EventArgs e)
        {
            try
            {
                // TODO: Open activity logs viewer
                MessageBox.Show("Activity logs viewer will be implemented here.", "View Logs", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening logs viewer");
            }
        }

        private void OnSettingsClick(object sender, EventArgs e)
        {
            try
            {
                // TODO: Open advanced settings dialog
                MessageBox.Show("Advanced settings dialog will be implemented here.", "Settings", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening settings");
            }
        }

        private void OnMonitoringSettingChanged(object sender, EventArgs e)
        {
            try
            {
                // TODO: Apply monitoring setting changes
                _logger.LogInformation("Monitoring settings changed by parent");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying monitoring settings");
            }
        }

        private async Task LoadRecentAlerts()
        {
            try
            {
                _recentAlertsListBox.Items.Clear();
                
                // TODO: Load recent alerts from server or local cache
                _recentAlertsListBox.Items.Add("Sample Alert: Inappropriate content detected at 2:30 PM");
                _recentAlertsListBox.Items.Add("Sample Alert: Blocked application 'game.exe' at 1:15 PM");
                _recentAlertsListBox.Items.Add("Sample Alert: USB device connected at 12:45 PM");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading recent alerts");
            }
        }

        private void UpdateMonitoringStatus()
        {
            try
            {
                _screenMonitoringCheckBox.Checked = _config.IsMonitoringEnabled("screen");
                _appMonitoringCheckBox.Checked = _config.IsMonitoringEnabled("application");
                _networkMonitoringCheckBox.Checked = _config.IsMonitoringEnabled("network");
                _captureIntervalNumeric.Value = _config.ServerConfig.Monitoring.ScreenCaptureInterval;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating monitoring status");
            }
        }

        private void UpdateUI()
        {
            if (_config.LocalConfig.ParentModeEnabled)
            {
                _loginPanel.Visible = false;
                _controlPanel.Visible = true;
            }
            else
            {
                _loginPanel.Visible = true;
                _controlPanel.Visible = false;
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_config.LocalConfig.ParentModeEnabled)
            {
                _securityService.DisableParentMode();
            }
            base.OnFormClosing(e);
        }
    }
}
