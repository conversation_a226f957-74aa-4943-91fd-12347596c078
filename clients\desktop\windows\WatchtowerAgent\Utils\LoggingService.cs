using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using WatchtowerAgent.Config;

namespace WatchtowerAgent.Utils
{
    public class LoggingService : IDisposable
    {
        private readonly AgentConfiguration _config;
        private readonly string _logDirectory;
        private readonly string _currentLogFile;
        private readonly SemaphoreSlim _logSemaphore;
        private readonly Timer _cleanupTimer;
        private bool _disposed = false;

        public LoggingService(AgentConfiguration config)
        {
            _config = config;
            _logDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData),
                "WatchtowerAgent",
                "Logs");
            
            Directory.CreateDirectory(_logDirectory);
            
            _currentLogFile = Path.Combine(_logDirectory, $"watchtower-{DateTime.Now:yyyy-MM-dd}.log");
            _logSemaphore = new SemaphoreSlim(1, 1);
            
            // Setup daily log cleanup timer
            _cleanupTimer = new Timer(CleanupOldLogs, null, TimeSpan.FromHours(1), TimeSpan.FromHours(24));
        }

        public async Task LogAsync(LogLevel level, string category, string message, Exception? exception = null)
        {
            try
            {
                await _logSemaphore.WaitAsync();
                
                var logEntry = CreateLogEntry(level, category, message, exception);
                await WriteLogEntryAsync(logEntry);
                
                // Also write to Windows Event Log for critical errors
                if (level >= LogLevel.Error)
                {
                    WriteToEventLog(level, message, exception);
                }
            }
            catch (Exception ex)
            {
                // Fallback logging to prevent infinite loops
                Console.WriteLine($"Logging error: {ex.Message}");
            }
            finally
            {
                _logSemaphore.Release();
            }
        }

        private string CreateLogEntry(LogLevel level, string category, string message, Exception? exception)
        {
            var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var levelStr = level.ToString().ToUpper();
            var threadId = Thread.CurrentThread.ManagedThreadId;
            
            var logBuilder = new StringBuilder();
            logBuilder.AppendLine($"[{timestamp}] [{levelStr}] [{category}] [Thread:{threadId}] {message}");
            
            if (exception != null)
            {
                logBuilder.AppendLine($"Exception: {exception.GetType().Name}: {exception.Message}");
                logBuilder.AppendLine($"StackTrace: {exception.StackTrace}");
                
                var innerEx = exception.InnerException;
                while (innerEx != null)
                {
                    logBuilder.AppendLine($"Inner Exception: {innerEx.GetType().Name}: {innerEx.Message}");
                    logBuilder.AppendLine($"Inner StackTrace: {innerEx.StackTrace}");
                    innerEx = innerEx.InnerException;
                }
            }
            
            return logBuilder.ToString();
        }

        private async Task WriteLogEntryAsync(string logEntry)
        {
            try
            {
                // Check file size and rotate if necessary
                if (File.Exists(_currentLogFile))
                {
                    var fileInfo = new FileInfo(_currentLogFile);
                    if (fileInfo.Length > _config.LocalConfig.MaxLogFileSizeMB * 1024 * 1024)
                    {
                        await RotateLogFileAsync();
                    }
                }
                
                await File.AppendAllTextAsync(_currentLogFile, logEntry, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to write log entry: {ex.Message}");
            }
        }

        private async Task RotateLogFileAsync()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd-HH-mm-ss");
                var rotatedFile = Path.Combine(_logDirectory, $"watchtower-{timestamp}.log");
                
                if (File.Exists(_currentLogFile))
                {
                    File.Move(_currentLogFile, rotatedFile);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to rotate log file: {ex.Message}");
            }
        }

        private void WriteToEventLog(LogLevel level, string message, Exception? exception)
        {
            try
            {
                var eventLogLevel = level switch
                {
                    LogLevel.Critical => System.Diagnostics.EventLogEntryType.Error,
                    LogLevel.Error => System.Diagnostics.EventLogEntryType.Error,
                    LogLevel.Warning => System.Diagnostics.EventLogEntryType.Warning,
                    _ => System.Diagnostics.EventLogEntryType.Information
                };

                var fullMessage = exception != null ? $"{message}\n{exception}" : message;
                
                using var eventLog = new System.Diagnostics.EventLog("Application");
                eventLog.Source = "WatchtowerAgent";
                eventLog.WriteEntry(fullMessage, eventLogLevel);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to write to event log: {ex.Message}");
            }
        }

        private void CleanupOldLogs(object? state)
        {
            try
            {
                var retentionDays = _config.LocalConfig.LogRetentionDays;
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                
                var logFiles = Directory.GetFiles(_logDirectory, "*.log");
                
                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to cleanup old logs: {ex.Message}");
            }
        }

        public async Task<string[]> GetRecentLogsAsync(int maxLines = 100)
        {
            try
            {
                await _logSemaphore.WaitAsync();
                
                if (!File.Exists(_currentLogFile))
                {
                    return Array.Empty<string>();
                }
                
                var lines = await File.ReadAllLinesAsync(_currentLogFile);
                var startIndex = Math.Max(0, lines.Length - maxLines);
                
                var recentLines = new string[Math.Min(maxLines, lines.Length)];
                Array.Copy(lines, startIndex, recentLines, 0, recentLines.Length);
                
                return recentLines;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to get recent logs: {ex.Message}");
                return Array.Empty<string>();
            }
            finally
            {
                _logSemaphore.Release();
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _cleanupTimer?.Dispose();
                _logSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }

    public class FileLoggerProvider : ILoggerProvider
    {
        private readonly LoggingService _loggingService;

        public FileLoggerProvider(LoggingService loggingService)
        {
            _loggingService = loggingService;
        }

        public ILogger CreateLogger(string categoryName)
        {
            return new FileLogger(categoryName, _loggingService);
        }

        public void Dispose()
        {
            _loggingService?.Dispose();
        }
    }

    public class FileLogger : ILogger
    {
        private readonly string _categoryName;
        private readonly LoggingService _loggingService;

        public FileLogger(string categoryName, LoggingService loggingService)
        {
            _categoryName = categoryName;
            _loggingService = loggingService;
        }

        public IDisposable BeginScope<TState>(TState state) => null!;

        public bool IsEnabled(LogLevel logLevel) => true;

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            if (!IsEnabled(logLevel))
                return;

            var message = formatter(state, exception);
            _ = Task.Run(async () => await _loggingService.LogAsync(logLevel, _categoryName, message, exception));
        }
    }

    public static class ErrorHandler
    {
        private static ILogger? _logger;

        public static void Initialize(ILogger logger)
        {
            _logger = logger;
            
            // Setup global exception handlers
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
        }

        private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                _logger?.LogCritical(exception, "Unhandled exception occurred. Terminating: {IsTerminating}", e.IsTerminating);
                
                // Attempt to send critical error to server
                _ = Task.Run(async () => await SendCriticalErrorAsync(exception));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in unhandled exception handler: {ex.Message}");
            }
        }

        private static void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                _logger?.LogError(e.Exception, "Unobserved task exception occurred");
                e.SetObserved(); // Prevent the process from terminating
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in unobserved task exception handler: {ex.Message}");
            }
        }

        private static async Task SendCriticalErrorAsync(Exception? exception)
        {
            try
            {
                // TODO: Implement critical error reporting to server
                // This should be a fire-and-forget operation that doesn't depend on the main application state
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to send critical error: {ex.Message}");
            }
        }

        public static async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3, TimeSpan? delay = null)
        {
            var retryDelay = delay ?? TimeSpan.FromSeconds(1);
            Exception? lastException = null;

            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    _logger?.LogWarning(ex, "Operation failed on attempt {Attempt}/{MaxRetries}", attempt + 1, maxRetries + 1);

                    if (attempt < maxRetries)
                    {
                        await Task.Delay(retryDelay);
                        retryDelay = TimeSpan.FromMilliseconds(retryDelay.TotalMilliseconds * 2); // Exponential backoff
                    }
                }
            }

            throw lastException ?? new Exception("Operation failed after all retry attempts");
        }

        public static async Task ExecuteWithRetryAsync(Func<Task> operation, int maxRetries = 3, TimeSpan? delay = null)
        {
            await ExecuteWithRetryAsync(async () =>
            {
                await operation();
                return true;
            }, maxRetries, delay);
        }
    }
}
