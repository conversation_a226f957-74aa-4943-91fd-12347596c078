version: '3.8'

services:
  watchtower-server:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=watchtower
      - DB_USER=watchtower
      - DB_PASSWORD=watchtower_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key
      - ENCRYPTION_KEY=your-32-char-encryption-key-here
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs

  watchtower-dashboard:
    build:
      context: ./web-dashboard
      dockerfile: Dockerfile
    ports:
      - "3001:80"
    depends_on:
      - watchtower-server

  postgres:
    image: postgres:14
    environment:
      - POSTGRES_DB=watchtower
      - POSTGRES_USER=watchtower
      - POSTGRES_PASSWORD=watchtower_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
