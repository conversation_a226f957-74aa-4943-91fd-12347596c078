FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S watchtower && \
    adduser -S watchtower -u 1001

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads && \
    chown -R watchtower:watchtower /app

USER watchtower

EXPOSE 3000

CMD ["npm", "start"]
