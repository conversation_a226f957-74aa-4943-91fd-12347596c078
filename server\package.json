{"name": "watchtower-server", "version": "1.0.0", "description": "Watchtower monitoring server", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.0", "socket.io": "^4.7.0", "pg": "^8.11.0", "redis": "^4.6.0", "multer": "^1.4.4", "winston": "^3.10.0", "express-rate-limit": "^6.8.0", "joi": "^17.9.0", "crypto-js": "^4.1.1", "node-cron": "^3.0.2", "sharp": "^0.32.0", "axios": "^1.4.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "dotenv": "^16.3.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.2", "@types/pg": "^8.10.0", "@types/multer": "^1.4.7", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.3", "@types/crypto-js": "^4.1.1", "@types/node": "^20.4.0", "@types/jest": "^29.5.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.44.0", "jest": "^29.6.0", "nodemon": "^3.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.1.0"}}