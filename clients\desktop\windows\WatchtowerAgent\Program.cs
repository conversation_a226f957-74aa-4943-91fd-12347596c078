using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WatchtowerAgent.Services;
using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WatchtowerAgent
{
    internal static class Program
    {
        [STAThread]
        static async Task Main(string[] args)
        {
            // Enable visual styles for Windows Forms
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // Check if running as service or console app
                if (args.Length > 0 && args[0] == "--service")
                {
                    await RunAsService();
                }
                else
                {
                    await RunAsConsole();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to start Watchtower Agent: {ex.Message}", 
                    "Watchtower Agent", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static async Task RunAsService()
        {
            var host = CreateHostBuilder().UseWindowsService().Build();
            await host.RunAsync();
        }

        private static async Task RunAsConsole()
        {
            var host = CreateHostBuilder().Build();
            
            // Start the host in the background
            var hostTask = host.RunAsync();
            
            // Show system tray icon
            using var trayIcon = new NotifyIcon
            {
                Icon = SystemIcons.Application,
                Text = "Watchtower Agent",
                Visible = true
            };
            
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.Add("Status", null, (s, e) => ShowStatus());
            contextMenu.Items.Add("Settings", null, (s, e) => ShowSettings());
            contextMenu.Items.Add("-");
            contextMenu.Items.Add("Exit", null, (s, e) => Application.Exit());
            trayIcon.ContextMenuStrip = contextMenu;

            // Keep the application running
            Application.Run();
            
            await host.StopAsync();
        }

        private static IHostBuilder CreateHostBuilder() =>
            Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    services.AddSingleton<ApiClient>();
                    services.AddSingleton<NetworkMonitor>();
                    services.AddSingleton<ContentScanner>();
                    services.AddSingleton<AppMonitor>();
                    services.AddSingleton<SystemMonitor>();
                    services.AddHostedService<AgentService>();
                })
                .UseWindowsService();

        private static void ShowStatus()
        {
            MessageBox.Show("Watchtower Agent is running and monitoring this device.", 
                "Status", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private static void ShowSettings()
        {
            MessageBox.Show("Settings dialog will be implemented here.", 
                "Settings", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
