using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WatchtowerAgent.Services;
using WatchtowerAgent.Config;
using WatchtowerAgent.Utils;
using WatchtowerAgent.Forms;
using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Security.Principal;

namespace WatchtowerAgent
{
    internal static class Program
    {
        private static IServiceProvider? _serviceProvider;
        private static ILogger<Program>? _logger;

        [STAThread]
        static async Task Main(string[] args)
        {
            // Enable visual styles for Windows Forms
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // Check for administrator privileges
                if (!IsRunningAsAdministrator())
                {
                    MessageBox.Show("Watchtower Agent requires administrator privileges to function properly.",
                        "Administrator Required", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Initialize logging first
                await InitializeLoggingAsync();

                // Check if running as service or console app
                if (args.Length > 0 && args[0] == "--service")
                {
                    await RunAsService();
                }
                else if (args.Length > 0 && args[0] == "--parent")
                {
                    await ShowParentControl();
                }
                else
                {
                    await RunAsConsole();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogCritical(ex, "Fatal error starting Watchtower Agent");
                MessageBox.Show($"Failed to start Watchtower Agent: {ex.Message}",
                    "Watchtower Agent", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static bool IsRunningAsAdministrator()
        {
            var identity = WindowsIdentity.GetCurrent();
            var principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }

        private static async Task InitializeLoggingAsync()
        {
            try
            {
                var tempConfig = new AgentConfiguration(new Microsoft.Extensions.Logging.Abstractions.NullLogger<AgentConfiguration>());
                await tempConfig.InitializeAsync();

                var loggingService = new LoggingService(tempConfig);
                var loggerFactory = LoggerFactory.Create(builder =>
                {
                    builder.AddProvider(new FileLoggerProvider(loggingService));
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                _logger = loggerFactory.CreateLogger<Program>();
                ErrorHandler.Initialize(_logger);

                _logger.LogInformation("Watchtower Agent starting...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize logging: {ex.Message}");
            }
        }

        private static async Task RunAsService()
        {
            _logger?.LogInformation("Starting as Windows service");
            var host = CreateHostBuilder().UseWindowsService().Build();
            _serviceProvider = host.Services;
            await host.RunAsync();
        }

        private static async Task RunAsConsole()
        {
            _logger?.LogInformation("Starting as console application");
            var host = CreateHostBuilder().Build();
            _serviceProvider = host.Services;

            // Start the host in the background
            var hostTask = host.RunAsync();

            // Show system tray icon
            using var trayIcon = new NotifyIcon
            {
                Icon = SystemIcons.Shield,
                Text = "Watchtower Agent - Monitoring Active",
                Visible = true
            };

            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.Add("Status", null, (s, e) => ShowStatus());
            contextMenu.Items.Add("Parent Control", null, (s, e) => ShowParentControlAsync());
            contextMenu.Items.Add("-");
            contextMenu.Items.Add("View Logs", null, (s, e) => ShowLogs());
            contextMenu.Items.Add("-");
            contextMenu.Items.Add("Exit", null, (s, e) => ExitApplication());
            trayIcon.ContextMenuStrip = contextMenu;

            // Double-click to show parent control
            trayIcon.DoubleClick += (s, e) => ShowParentControlAsync();

            // Keep the application running
            Application.Run();

            await host.StopAsync();
        }

        private static async Task ShowParentControl()
        {
            _logger?.LogInformation("Starting parent control interface");

            var host = CreateHostBuilder().Build();
            _serviceProvider = host.Services;

            var config = _serviceProvider.GetRequiredService<AgentConfiguration>();
            var securityService = _serviceProvider.GetRequiredService<SecurityService>();
            var apiClient = _serviceProvider.GetRequiredService<ApiClient>();
            var logger = _serviceProvider.GetRequiredService<ILogger<ParentControlForm>>();

            var parentForm = new ParentControlForm(logger, config, securityService, apiClient);
            Application.Run(parentForm);
        }

        private static IHostBuilder CreateHostBuilder() =>
            Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Configuration
                    services.AddSingleton<AgentConfiguration>();

                    // Core services
                    services.AddSingleton<ApiClient>();
                    services.AddSingleton<SecurityService>();

                    // Monitoring services
                    services.AddSingleton<NetworkMonitor>();
                    services.AddSingleton<ContentScanner>();
                    services.AddSingleton<AppMonitor>();
                    services.AddSingleton<SystemMonitor>();

                    // Utilities
                    services.AddSingleton<LoggingService>();

                    // Main service
                    services.AddHostedService<AgentService>();
                })
                .ConfigureLogging(logging =>
                {
                    logging.ClearProviders();
                    logging.AddConsole();
                    logging.SetMinimumLevel(LogLevel.Information);
                })
                .UseWindowsService();

        private static void ShowStatus()
        {
            try
            {
                var message = "Watchtower Agent is running and monitoring this device.\n\n" +
                             "Active Monitoring:\n" +
                             "• Application monitoring\n" +
                             "• Network traffic monitoring\n" +
                             "• Screen content scanning\n" +
                             "• File system monitoring\n" +
                             "• System event monitoring";

                MessageBox.Show(message, "Watchtower Agent Status",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error showing status");
            }
        }

        private static async void ShowParentControlAsync()
        {
            try
            {
                if (_serviceProvider == null)
                {
                    MessageBox.Show("Service not initialized.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var config = _serviceProvider.GetRequiredService<AgentConfiguration>();
                var securityService = _serviceProvider.GetRequiredService<SecurityService>();
                var apiClient = _serviceProvider.GetRequiredService<ApiClient>();
                var logger = _serviceProvider.GetRequiredService<ILogger<ParentControlForm>>();

                var parentForm = new ParentControlForm(logger, config, securityService, apiClient);
                parentForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error showing parent control");
                MessageBox.Show("Error opening parent control interface.", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static async void ShowLogs()
        {
            try
            {
                if (_serviceProvider == null) return;

                var loggingService = _serviceProvider.GetRequiredService<LoggingService>();
                var recentLogs = await loggingService.GetRecentLogsAsync(50);

                var logText = string.Join(Environment.NewLine, recentLogs);

                var logForm = new Form
                {
                    Text = "Watchtower Agent Logs",
                    Size = new System.Drawing.Size(800, 600),
                    StartPosition = FormStartPosition.CenterScreen
                };

                var textBox = new TextBox
                {
                    Multiline = true,
                    ScrollBars = ScrollBars.Both,
                    Dock = DockStyle.Fill,
                    Text = logText,
                    ReadOnly = true,
                    Font = new System.Drawing.Font("Consolas", 9)
                };

                logForm.Controls.Add(textBox);
                logForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error showing logs");
            }
        }

        private static void ExitApplication()
        {
            try
            {
                var result = MessageBox.Show(
                    "Are you sure you want to exit Watchtower Agent?\nThis will stop all monitoring activities.",
                    "Confirm Exit",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _logger?.LogInformation("Watchtower Agent shutting down by user request");
                    Application.Exit();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during application exit");
                Application.Exit();
            }
        }
    }
}
