using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using System;
using System.Diagnostics;
using System.IO;
using System.Management;
using System.Runtime.InteropServices;
using System.Security.Principal;
using System.ServiceProcess;
using System.Threading.Tasks;
using System.Timers;
using WatchtowerAgent.Config;
using WatchtowerAgent.Models;
using Timer = System.Timers.Timer;

namespace WatchtowerAgent.Services
{
    public class SecurityService : IDisposable
    {
        private readonly ILogger<SecurityService> _logger;
        private readonly AgentConfiguration _config;
        private readonly ApiClient _apiClient;
        private readonly Timer _protectionTimer;
        private ManagementEventWatcher? _processWatcher;
        private bool _isRunning = false;
        private bool _disposed = false;

        // Windows API imports for process protection
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetCurrentProcess();

        [DllImport("advapi32.dll", SetLastError = true)]
        private static extern bool SetKernelObjectSecurity(IntPtr handle, int securityInformation, IntPtr securityDescriptor);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool SetProcessShutdownParameters(uint dwLevel, uint dwFlags);

        [DllImport("user32.dll")]
        private static extern bool BlockInput(bool fBlockIt);

        [DllImport("user32.dll")]
        private static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        private static extern bool EnableWindow(IntPtr hWnd, bool bEnable);

        private const int SW_HIDE = 0;
        private const int SW_SHOW = 5;

        public SecurityService(ILogger<SecurityService> logger, AgentConfiguration config, ApiClient apiClient)
        {
            _logger = logger;
            _config = config;
            _apiClient = apiClient;
            
            _protectionTimer = new Timer(10000); // Check every 10 seconds
            _protectionTimer.Elapsed += OnProtectionTimerElapsed;
        }

        public async Task StartAsync()
        {
            if (_isRunning) return;

            _logger.LogInformation("Starting security service...");

            try
            {
                // Set process protection
                SetProcessProtection();
                
                // Start monitoring for tampering attempts
                StartTamperMonitoring();
                
                // Disable dangerous system tools
                DisableSystemTools();
                
                // Start protection timer
                _protectionTimer.Start();
                
                _isRunning = true;
                _logger.LogInformation("Security service started successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start security service");
                throw;
            }
        }

        public async Task StopAsync()
        {
            if (!_isRunning) return;

            _logger.LogInformation("Stopping security service...");

            _protectionTimer.Stop();
            StopTamperMonitoring();
            
            // Re-enable system tools when stopping (for maintenance)
            EnableSystemTools();
            
            _isRunning = false;
            _logger.LogInformation("Security service stopped");
        }

        private void SetProcessProtection()
        {
            try
            {
                // Set high shutdown priority to ensure we're notified before termination
                SetProcessShutdownParameters(0x3FF, 0);
                
                // Make the process critical (requires admin privileges)
                var currentProcess = GetCurrentProcess();
                
                _logger.LogInformation("Process protection enabled");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to set full process protection");
            }
        }

        private void StartTamperMonitoring()
        {
            try
            {
                // Monitor for processes that might be used to terminate the agent
                var query = new WqlEventQuery("SELECT * FROM Win32_ProcessStartTrace WHERE ProcessName = 'taskkill.exe' OR ProcessName = 'taskmgr.exe' OR ProcessName = 'cmd.exe' OR ProcessName = 'powershell.exe'");
                _processWatcher = new ManagementEventWatcher(query);
                _processWatcher.EventArrived += OnSuspiciousProcessStarted;
                _processWatcher.Start();
                
                _logger.LogDebug("Tamper monitoring started");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to start tamper monitoring");
            }
        }

        private void StopTamperMonitoring()
        {
            try
            {
                _processWatcher?.Stop();
                _processWatcher?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error stopping tamper monitoring");
            }
        }

        private void OnSuspiciousProcessStarted(object sender, EventArrivedEventArgs e)
        {
            try
            {
                var processName = e.NewEvent["ProcessName"]?.ToString();
                var processId = Convert.ToInt32(e.NewEvent["ProcessID"]);
                
                _logger.LogWarning($"Suspicious process detected: {processName} (PID: {processId})");
                
                // Log the tampering attempt
                var tamperEvent = new MonitoringEvent
                {
                    DeviceId = _config.DeviceId,
                    EventType = "TamperingAttempt",
                    Category = "Security",
                    Description = $"Suspicious process started: {processName}",
                    Data = new Dictionary<string, object>
                    {
                        ["ProcessName"] = processName ?? "Unknown",
                        ["ProcessId"] = processId,
                        ["UserName"] = Environment.UserName
                    },
                    Severity = "Critical",
                    RequiresParentNotification = true
                };

                _ = Task.Run(async () => await _apiClient.SendEventAsync(tamperEvent));
                
                // Take protective action based on the process
                HandleSuspiciousProcess(processName, processId);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error handling suspicious process event");
            }
        }

        private void HandleSuspiciousProcess(string? processName, int processId)
        {
            try
            {
                switch (processName?.ToLower())
                {
                    case "taskmgr.exe":
                        // Hide or disable Task Manager
                        DisableTaskManager();
                        break;
                        
                    case "taskkill.exe":
                        // Terminate the taskkill process
                        TerminateProcess(processId);
                        break;
                        
                    case "cmd.exe":
                    case "powershell.exe":
                        // Monitor command line processes more closely
                        MonitorCommandLineProcess(processId);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Error handling suspicious process: {processName}");
            }
        }

        private void DisableTaskManager()
        {
            try
            {
                // Find and hide Task Manager window
                var taskMgrWindow = FindWindow("TaskManagerWindow", null);
                if (taskMgrWindow != IntPtr.Zero)
                {
                    ShowWindow(taskMgrWindow, SW_HIDE);
                    EnableWindow(taskMgrWindow, false);
                }
                
                // Disable Task Manager via registry (requires admin)
                using var key = Registry.CurrentUser.CreateSubKey(@"Software\Microsoft\Windows\CurrentVersion\Policies\System");
                key.SetValue("DisableTaskMgr", 1, RegistryValueKind.DWord);
                
                _logger.LogInformation("Task Manager disabled");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to disable Task Manager");
            }
        }

        private void TerminateProcess(int processId)
        {
            try
            {
                var process = Process.GetProcessById(processId);
                process.Kill();
                _logger.LogInformation($"Terminated suspicious process: PID {processId}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Failed to terminate process: PID {processId}");
            }
        }

        private void MonitorCommandLineProcess(int processId)
        {
            try
            {
                var process = Process.GetProcessById(processId);
                var commandLine = GetProcessCommandLine(processId);
                
                // Check for dangerous commands
                var dangerousCommands = new[] { "taskkill", "net stop", "sc stop", "wmic", "reg delete" };
                
                foreach (var cmd in dangerousCommands)
                {
                    if (commandLine.Contains(cmd, StringComparison.OrdinalIgnoreCase))
                    {
                        _logger.LogWarning($"Dangerous command detected: {commandLine}");
                        TerminateProcess(processId);
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Error monitoring command line process: PID {processId}");
            }
        }

        private string GetProcessCommandLine(int processId)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher($"SELECT CommandLine FROM Win32_Process WHERE ProcessId = {processId}");
                using var objects = searcher.Get();
                
                foreach (ManagementObject obj in objects)
                {
                    return obj["CommandLine"]?.ToString() ?? "";
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, $"Failed to get command line for PID {processId}");
            }
            
            return "";
        }

        private void DisableSystemTools()
        {
            try
            {
                // Disable registry editor
                using var regKey = Registry.CurrentUser.CreateSubKey(@"Software\Microsoft\Windows\CurrentVersion\Policies\System");
                regKey.SetValue("DisableRegistryTools", 1, RegistryValueKind.DWord);
                
                // Disable command prompt
                using var cmdKey = Registry.CurrentUser.CreateSubKey(@"Software\Policies\Microsoft\Windows\System");
                cmdKey.SetValue("DisableCMD", 2, RegistryValueKind.DWord);
                
                // Disable control panel
                using var cpKey = Registry.CurrentUser.CreateSubKey(@"Software\Microsoft\Windows\CurrentVersion\Policies\Explorer");
                cpKey.SetValue("NoControlPanel", 1, RegistryValueKind.DWord);
                
                _logger.LogInformation("System tools disabled");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to disable some system tools");
            }
        }

        private void EnableSystemTools()
        {
            try
            {
                // Re-enable registry editor
                using var regKey = Registry.CurrentUser.CreateSubKey(@"Software\Microsoft\Windows\CurrentVersion\Policies\System");
                regKey.DeleteValue("DisableRegistryTools", false);
                regKey.DeleteValue("DisableTaskMgr", false);
                
                // Re-enable command prompt
                using var cmdKey = Registry.CurrentUser.CreateSubKey(@"Software\Policies\Microsoft\Windows\System");
                cmdKey.DeleteValue("DisableCMD", false);
                
                // Re-enable control panel
                using var cpKey = Registry.CurrentUser.CreateSubKey(@"Software\Microsoft\Windows\CurrentVersion\Policies\Explorer");
                cpKey.DeleteValue("NoControlPanel", false);
                
                _logger.LogInformation("System tools re-enabled");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to re-enable some system tools");
            }
        }

        private async void OnProtectionTimerElapsed(object sender, ElapsedEventArgs e)
        {
            try
            {
                // Check if the agent service is still running
                await VerifyServiceIntegrity();
                
                // Check for unauthorized registry changes
                CheckRegistryIntegrity();
                
                // Verify file integrity
                CheckFileIntegrity();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during protection check");
            }
        }

        private async Task VerifyServiceIntegrity()
        {
            try
            {
                // Check if running as a service
                var serviceName = "WatchtowerAgent";
                var service = ServiceController.GetServices()
                    .FirstOrDefault(s => s.ServiceName.Equals(serviceName, StringComparison.OrdinalIgnoreCase));
                
                if (service != null && service.Status != ServiceControllerStatus.Running)
                {
                    _logger.LogWarning("Service is not running, attempting to restart");
                    
                    var restartEvent = new MonitoringEvent
                    {
                        DeviceId = _config.DeviceId,
                        EventType = "ServiceRestart",
                        Category = "Security",
                        Description = "Agent service was stopped and restarted",
                        Severity = "Warning",
                        RequiresParentNotification = true
                    };
                    
                    await _apiClient.SendEventAsync(restartEvent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error verifying service integrity");
            }
        }

        private void CheckRegistryIntegrity()
        {
            try
            {
                // Verify critical registry settings haven't been tampered with
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\WatchtowerAgent");
                if (key == null)
                {
                    _logger.LogWarning("Critical registry key missing - possible tampering detected");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error checking registry integrity");
            }
        }

        private void CheckFileIntegrity()
        {
            try
            {
                var agentPath = Process.GetCurrentProcess().MainModule?.FileName;
                if (!string.IsNullOrEmpty(agentPath) && !File.Exists(agentPath))
                {
                    _logger.LogCritical("Agent executable file missing - critical tampering detected");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error checking file integrity");
            }
        }

        public bool AuthenticateParent(string password)
        {
            try
            {
                // Simple password authentication for parent access
                // In production, this should use proper hashing and salting
                var storedHash = GetStoredParentPasswordHash();
                var inputHash = ComputeHash(password);
                
                var isAuthenticated = storedHash == inputHash;
                
                if (isAuthenticated)
                {
                    _config.LocalConfig.LastParentLogin = DateTime.UtcNow;
                    _config.LocalConfig.ParentModeEnabled = true;
                    _logger.LogInformation("Parent authenticated successfully");
                }
                else
                {
                    _logger.LogWarning("Failed parent authentication attempt");
                }
                
                return isAuthenticated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during parent authentication");
                return false;
            }
        }

        public void DisableParentMode()
        {
            _config.LocalConfig.ParentModeEnabled = false;
            _logger.LogInformation("Parent mode disabled");
        }

        private string GetStoredParentPasswordHash()
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\WatchtowerAgent");
                var encryptedHash = key?.GetValue("ParentPasswordHash") as byte[];
                
                if (encryptedHash != null)
                {
                    var decryptedHash = System.Security.Cryptography.ProtectedData.Unprotect(
                        encryptedHash, null, System.Security.Cryptography.DataProtectionScope.LocalMachine);
                    return System.Text.Encoding.UTF8.GetString(decryptedHash);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving parent password hash");
            }
            
            return string.Empty;
        }

        private string ComputeHash(string input)
        {
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(input));
            return Convert.ToBase64String(hash);
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopTamperMonitoring();
                _protectionTimer?.Dispose();
                _disposed = true;
            }
        }
    }
}
