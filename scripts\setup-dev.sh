#!/bin/bash

echo "🚀 Setting up Watchtower development environment..."

# Install root dependencies
echo "📦 Installing root dependencies..."
npm install

# Setup server
echo "🔧 Setting up server..."
cd server
npm install
cp .env.example .env
echo "⚠️  Please edit server/.env with your configuration"
cd ..

# Setup web dashboard
echo "🌐 Setting up web dashboard..."
cd web-dashboard
npm install
cd ..

# Create database
echo "🗄️  Setting up database..."
if command -v psql &> /dev/null; then
    createdb watchtower 2>/dev/null || echo "Database might already exist"
    echo "✅ Database setup complete"
else
    echo "⚠️  PostgreSQL not found. Please install PostgreSQL and create database manually"
fi

# Create logs directory
mkdir -p logs

echo "✅ Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit server/.env with your database credentials"
echo "2. Run 'npm run dev' to start development servers"
echo "3. Open http://localhost:3001 for the dashboard"
echo "4. Open http://localhost:3000/api for the API"
