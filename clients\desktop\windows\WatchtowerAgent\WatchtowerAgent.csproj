<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <OutputType>WinExe</OutputType>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationIcon>Resources\watchtower.ico</ApplicationIcon>
    <AssemblyTitle>Watchtower Agent</AssemblyTitle>
    <AssemblyDescription>Watchtower monitoring agent for Windows</AssemblyDescription>
    <AssemblyConfiguration>$(Configuration)</AssemblyConfiguration>
    <AssemblyCompany>Watchtower Systems</AssemblyCompany>
    <AssemblyProduct>Watchtower</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="7.0.2" />
    <PackageReference Include="SharpPcap" Version="6.2.5" />
    <PackageReference Include="PacketDotNet" Version="1.4.7" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="System.Security.Cryptography.ProtectedData" Version="7.0.1" />
    <PackageReference Include="System.IO.FileSystem.Watcher" Version="4.3.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="7.0.0" />
    <PackageReference Include="Tesseract" Version="5.2.0" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="7.0.1" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
