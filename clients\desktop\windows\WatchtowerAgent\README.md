# Watchtower Agent for Windows

A comprehensive parental control monitoring agent for Windows that provides real-time monitoring and content filtering capabilities.

## Features

### Core Monitoring
- **Application Monitoring**: Track running processes, block unauthorized applications, monitor installation attempts
- **Screen Content Scanning**: Capture and analyze screen content using OCR for inappropriate text detection
- **Network Traffic Monitoring**: Monitor and filter network connections, block inappropriate websites
- **File System Monitoring**: Track file creation, deletion, and modification in key directories
- **System Event Monitoring**: Monitor USB device connections, user logons, system shutdowns

### Security & Protection
- **Tamper Protection**: Prevent unauthorized termination or bypassing of the agent
- **Process Protection**: Secure the agent process against termination attempts
- **Registry Protection**: Disable system tools that could be used to bypass monitoring
- **Parent Authentication**: Secure access to management features with password protection

### Parent Management
- **Local Control Interface**: Windows Forms-based UI for on-the-spot management
- **Temporary Disable**: Allow parents to temporarily disable monitoring
- **Real-time Alerts**: View recent security alerts and monitoring events
- **Settings Management**: Configure monitoring parameters and restrictions

### Communication
- **Server Integration**: Secure communication with Watchtower server
- **Real-time Reporting**: Send monitoring events and alerts to central server
- **Configuration Sync**: Receive policy updates from server
- **Heartbeat Monitoring**: Regular status updates to ensure agent connectivity

## Architecture

### Services
- **AgentService**: Main coordination service that manages all monitoring activities
- **ApiClient**: Handles secure communication with the Watchtower server
- **AppMonitor**: Monitors running applications and processes
- **ContentScanner**: Captures and analyzes screen content for inappropriate material
- **NetworkMonitor**: Tracks and filters network traffic
- **SystemMonitor**: Monitors file system changes and system events
- **SecurityService**: Provides tamper protection and parent authentication

### Configuration
- **AgentConfiguration**: Centralized configuration management with encrypted storage
- **Registry Integration**: Secure storage of device ID and API keys
- **Server Sync**: Automatic synchronization of policies from server

### Logging & Error Handling
- **Comprehensive Logging**: File-based logging with rotation and cleanup
- **Error Recovery**: Automatic retry mechanisms and graceful error handling
- **Event Log Integration**: Critical errors logged to Windows Event Log

## Installation & Setup

### Prerequisites
- Windows 10/11 or Windows Server 2019+
- .NET 6.0 Runtime
- Administrator privileges
- Network connectivity to Watchtower server

### Installation Steps
1. Run the installer as Administrator
2. Configure server connection details
3. Set parent password for management access
4. Install as Windows service for automatic startup
5. Verify monitoring is active via system tray

### Configuration
The agent can be configured through:
- `appsettings.json` for default settings
- Registry for secure device-specific settings
- Server-side policy management
- Local parent control interface

## Usage

### Running as Service
```bash
WatchtowerAgent.exe --service
```

### Running as Console Application
```bash
WatchtowerAgent.exe
```

### Parent Control Access
```bash
WatchtowerAgent.exe --parent
```

### System Tray
When running as console application, the agent provides a system tray icon with:
- Status information
- Parent control access
- Log viewer
- Exit option (with confirmation)

## Security Considerations

### Protection Mechanisms
- Process protection against termination
- Registry key protection
- System tool disabling (Task Manager, Registry Editor, Command Prompt)
- File integrity monitoring
- Tamper attempt detection and reporting

### Parent Authentication
- Secure password-based authentication
- Encrypted password storage
- Session management for parent mode
- Automatic logout after inactivity

### Data Protection
- Encrypted configuration storage
- Secure API communication with server
- Protected data transmission
- Local data encryption using Windows DPAPI

## Monitoring Capabilities

### Application Control
- Real-time process monitoring
- Application blocking based on policies
- Installation attempt detection
- Window title and executable path tracking
- Usage time tracking per application

### Content Filtering
- Screen capture and OCR analysis
- Keyword detection in displayed content
- Image analysis for inappropriate content
- Real-time content blocking capabilities
- Configurable sensitivity levels

### Network Monitoring
- Packet-level traffic analysis
- Domain and IP blocking
- P2P traffic detection and blocking
- Bandwidth monitoring
- Connection logging and reporting

### System Monitoring
- File system change tracking
- USB device connection monitoring
- User logon/logoff tracking
- System shutdown monitoring
- Registry change detection

## Logging

### Log Files
- Location: `%ProgramData%\WatchtowerAgent\Logs\`
- Format: Structured text logs with timestamps
- Rotation: Automatic rotation based on file size
- Retention: Configurable retention period (default 30 days)

### Log Levels
- **Critical**: System failures, security breaches
- **Error**: Service errors, communication failures
- **Warning**: Policy violations, suspicious activities
- **Information**: Normal operations, status updates
- **Debug**: Detailed diagnostic information

## Troubleshooting

### Common Issues
1. **Service won't start**: Check administrator privileges and .NET runtime
2. **Server connection failed**: Verify network connectivity and server URL
3. **Monitoring not working**: Check service status and configuration
4. **Parent control access denied**: Verify password and authentication

### Diagnostic Tools
- Built-in log viewer in system tray menu
- Windows Event Log integration
- Service status monitoring
- Configuration validation

## Development

### Building
```bash
dotnet build WatchtowerAgent.csproj
```

### Testing
```bash
dotnet test
```

### Debugging
- Enable debug mode in configuration
- Use Visual Studio debugger
- Check log files for detailed information
- Monitor Windows Event Log

## Dependencies

### NuGet Packages
- Microsoft.Extensions.Hosting
- Microsoft.Extensions.Logging
- Microsoft.Extensions.Configuration
- Newtonsoft.Json
- System.Management
- SharpPcap
- PacketDotNet
- Tesseract (OCR)
- System.Drawing.Common

### System Requirements
- Windows 10 version 1809 or later
- .NET 6.0 Runtime
- 100MB available disk space
- 256MB RAM minimum
- Network adapter for traffic monitoring

## License

This software is part of the Watchtower parental control system. See the main project license for details.

## Support

For technical support and documentation, please refer to the main Watchtower project documentation or contact the development team.
