using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Generic;
using WatchtowerAgent.Models;
using WatchtowerAgent.Config;
using System.Security.Cryptography;
using System.Net.Http.Headers;

namespace WatchtowerAgent.Services
{
    public class ApiClient : IDisposable
    {
        private readonly ILogger<ApiClient> _logger;
        private readonly HttpClient _httpClient;
        private readonly AgentConfiguration _config;
        private string? _authToken;
        private DateTime _tokenExpiry;
        private bool _disposed = false;

        public ApiClient(ILogger<ApiClient> logger, AgentConfiguration config)
        {
            _logger = logger;
            _config = config;
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        public async Task InitializeAsync()
        {
            _logger.LogInformation("Initializing API client...");
            
            // Set base address
            _httpClient.BaseAddress = new Uri(_config.ServerUrl);
            
            // Set default headers
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "WatchtowerAgent/1.0");
            _httpClient.DefaultRequestHeaders.Add("X-Agent-Version", "1.0.0");
            _httpClient.DefaultRequestHeaders.Add("X-Device-Id", _config.DeviceId);
            
            // Authenticate with server
            await AuthenticateAsync();
            
            _logger.LogInformation("API client initialized successfully");
        }

        private async Task AuthenticateAsync()
        {
            try
            {
                var authRequest = new
                {
                    DeviceId = _config.DeviceId,
                    DeviceName = Environment.MachineName,
                    AgentVersion = "1.0.0",
                    Platform = "Windows",
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    Signature = GenerateSignature(_config.DeviceId, _config.ApiKey)
                };

                var response = await PostAsync<AuthResponse>("/api/agent/auth", authRequest);
                
                if (response?.Success == true && !string.IsNullOrEmpty(response.Token))
                {
                    _authToken = response.Token;
                    _tokenExpiry = DateTime.UtcNow.AddSeconds(response.ExpiresIn);
                    
                    // Set authorization header
                    _httpClient.DefaultRequestHeaders.Authorization = 
                        new AuthenticationHeaderValue("Bearer", _authToken);
                    
                    _logger.LogInformation("Authentication successful");
                }
                else
                {
                    throw new Exception($"Authentication failed: {response?.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to authenticate with server");
                throw;
            }
        }

        public async Task SendHeartbeatAsync()
        {
            try
            {
                // Check if token needs refresh
                if (DateTime.UtcNow.AddMinutes(5) >= _tokenExpiry)
                {
                    await AuthenticateAsync();
                }

                var heartbeat = new
                {
                    DeviceId = _config.DeviceId,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    Status = "active",
                    SystemInfo = await GetSystemInfoAsync()
                };

                await PostAsync("/api/agent/heartbeat", heartbeat);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send heartbeat");
            }
        }

        public async Task<ServerConfiguration?> GetConfigurationAsync()
        {
            try
            {
                var response = await GetAsync<ServerConfiguration>($"/api/agent/config/{_config.DeviceId}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get configuration from server");
                return null;
            }
        }

        public async Task SendEventAsync(MonitoringEvent monitoringEvent)
        {
            try
            {
                await PostAsync("/api/agent/events", monitoringEvent);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send event to server");
            }
        }

        public async Task SendBulkEventsAsync(IEnumerable<MonitoringEvent> events)
        {
            try
            {
                await PostAsync("/api/agent/events/bulk", events);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send bulk events to server");
            }
        }

        public async Task<PolicyResponse?> CheckPolicyAsync(string resourceType, string resourceValue)
        {
            try
            {
                var request = new
                {
                    DeviceId = _config.DeviceId,
                    ResourceType = resourceType,
                    ResourceValue = resourceValue,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                return await PostAsync<PolicyResponse>("/api/agent/policy/check", request);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to check policy");
                return null;
            }
        }

        private async Task<T?> GetAsync<T>(string endpoint) where T : class
        {
            var response = await _httpClient.GetAsync(endpoint);
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<T>(content);
        }

        private async Task<T?> PostAsync<T>(string endpoint, object data) where T : class
        {
            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync(endpoint, content);
            response.EnsureSuccessStatusCode();
            
            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<T>(responseContent);
        }

        private async Task PostAsync(string endpoint, object data)
        {
            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync(endpoint, content);
            response.EnsureSuccessStatusCode();
        }

        private string GenerateSignature(string deviceId, string apiKey)
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
            var message = $"{deviceId}:{timestamp}";
            
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(apiKey));
            var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(message));
            return Convert.ToBase64String(hash);
        }

        private async Task<object> GetSystemInfoAsync()
        {
            return new
            {
                MachineName = Environment.MachineName,
                UserName = Environment.UserName,
                OSVersion = Environment.OSVersion.ToString(),
                ProcessorCount = Environment.ProcessorCount,
                WorkingSet = Environment.WorkingSet,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _httpClient?.Dispose();
                _disposed = true;
            }
        }
    }
}
