using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Management;
using System.Threading.Tasks;
using System.Timers;
using WatchtowerAgent.Config;
using WatchtowerAgent.Models;
using Timer = System.Timers.Timer;

namespace WatchtowerAgent.Services
{
    public class SystemMonitor : IDisposable
    {
        private readonly ILogger<SystemMonitor> _logger;
        private readonly AgentConfiguration _config;
        private readonly ApiClient _apiClient;
        private readonly Timer _statsTimer;
        private readonly List<MonitoringEvent> _eventQueue;
        private readonly Dictionary<string, FileSystemWatcher> _fileWatchers;
        
        private ManagementEventWatcher? _usbWatcher;
        private ManagementEventWatcher? _logonWatcher;
        private ManagementEventWatcher? _shutdownWatcher;
        private bool _isRunning = false;
        private bool _disposed = false;

        public SystemMonitor(ILogger<SystemMonitor> logger, AgentConfiguration config, ApiClient apiClient)
        {
            _logger = logger;
            _config = config;
            _apiClient = apiClient;
            _eventQueue = new List<MonitoringEvent>();
            _fileWatchers = new Dictionary<string, FileSystemWatcher>();
            
            _statsTimer = new Timer(60000); // Send stats every minute
            _statsTimer.Elapsed += OnStatsTimerElapsed;
        }

        public async Task StartAsync()
        {
            if (_isRunning) return;

            _logger.LogInformation("Starting system monitor...");

            try
            {
                // Start file system monitoring
                if (_config.IsMonitoringEnabled("filesystem"))
                {
                    StartFileSystemMonitoring();
                }
                
                // Start USB device monitoring
                StartUsbDeviceMonitoring();
                
                // Start system event monitoring
                StartSystemEventMonitoring();
                
                // Start statistics timer
                _statsTimer.Start();
                
                _isRunning = true;
                _logger.LogInformation("System monitor started successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start system monitor");
                throw;
            }
        }

        public async Task StopAsync()
        {
            if (!_isRunning) return;

            _logger.LogInformation("Stopping system monitor...");

            _statsTimer.Stop();
            StopFileSystemMonitoring();
            StopUsbDeviceMonitoring();
            StopSystemEventMonitoring();
            
            // Send any remaining events
            await FlushEventQueueAsync();
            
            _isRunning = false;
            _logger.LogInformation("System monitor stopped");
        }

        private void StartFileSystemMonitoring()
        {
            try
            {
                // Monitor common user directories
                var directoriesToMonitor = new[]
                {
                    Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                    Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                    Environment.GetFolderPath(Environment.SpecialFolder.MyPictures),
                    Environment.GetFolderPath(Environment.SpecialFolder.MyVideos),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "Downloads")
                };

                foreach (var directory in directoriesToMonitor)
                {
                    if (Directory.Exists(directory))
                    {
                        var watcher = new FileSystemWatcher(directory)
                        {
                            IncludeSubdirectories = true,
                            NotifyFilter = NotifyFilters.CreationTime | NotifyFilters.LastWrite | 
                                         NotifyFilters.FileName | NotifyFilters.DirectoryName
                        };

                        watcher.Created += OnFileSystemEvent;
                        watcher.Deleted += OnFileSystemEvent;
                        watcher.Renamed += OnFileSystemRenamed;
                        watcher.Changed += OnFileSystemEvent;

                        watcher.EnableRaisingEvents = true;
                        _fileWatchers[directory] = watcher;
                        
                        _logger.LogDebug($"Started monitoring directory: {directory}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to start file system monitoring");
            }
        }

        private void StopFileSystemMonitoring()
        {
            foreach (var watcher in _fileWatchers.Values)
            {
                try
                {
                    watcher.EnableRaisingEvents = false;
                    watcher.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error stopping file system watcher");
                }
            }
            _fileWatchers.Clear();
        }

        private void StartUsbDeviceMonitoring()
        {
            try
            {
                var query = new WqlEventQuery("SELECT * FROM Win32_VolumeChangeEvent WHERE EventType = 2 OR EventType = 3");
                _usbWatcher = new ManagementEventWatcher(query);
                _usbWatcher.EventArrived += OnUsbDeviceEvent;
                _usbWatcher.Start();
                
                _logger.LogDebug("Started USB device monitoring");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to start USB device monitoring");
            }
        }

        private void StopUsbDeviceMonitoring()
        {
            try
            {
                _usbWatcher?.Stop();
                _usbWatcher?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error stopping USB device monitoring");
            }
        }

        private void StartSystemEventMonitoring()
        {
            try
            {
                // Monitor user logon events
                var logonQuery = new WqlEventQuery("SELECT * FROM Win32_LogonSession");
                _logonWatcher = new ManagementEventWatcher(logonQuery);
                _logonWatcher.EventArrived += OnLogonEvent;
                _logonWatcher.Start();

                // Monitor system shutdown events
                var shutdownQuery = new WqlEventQuery("SELECT * FROM Win32_SystemShutdownEvent");
                _shutdownWatcher = new ManagementEventWatcher(shutdownQuery);
                _shutdownWatcher.EventArrived += OnShutdownEvent;
                _shutdownWatcher.Start();
                
                _logger.LogDebug("Started system event monitoring");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to start system event monitoring");
            }
        }

        private void StopSystemEventMonitoring()
        {
            try
            {
                _logonWatcher?.Stop();
                _logonWatcher?.Dispose();
                _shutdownWatcher?.Stop();
                _shutdownWatcher?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error stopping system event monitoring");
            }
        }

        private void OnFileSystemEvent(object sender, FileSystemEventArgs e)
        {
            try
            {
                var eventType = e.ChangeType switch
                {
                    WatcherChangeTypes.Created => "FileCreated",
                    WatcherChangeTypes.Deleted => "FileDeleted",
                    WatcherChangeTypes.Changed => "FileModified",
                    _ => "FileSystemEvent"
                };

                var monitoringEvent = new MonitoringEvent
                {
                    DeviceId = _config.DeviceId,
                    EventType = eventType,
                    Category = "FileSystem",
                    Description = $"File system event: {e.ChangeType} - {Path.GetFileName(e.FullPath)}",
                    Data = new Dictionary<string, object>
                    {
                        ["Path"] = e.FullPath,
                        ["FileName"] = Path.GetFileName(e.FullPath),
                        ["Directory"] = Path.GetDirectoryName(e.FullPath) ?? "",
                        ["Extension"] = Path.GetExtension(e.FullPath),
                        ["ChangeType"] = e.ChangeType.ToString()
                    },
                    Severity = "Info"
                };

                // Check for suspicious file types
                var suspiciousExtensions = new[] { ".exe", ".bat", ".cmd", ".scr", ".pif", ".com" };
                var extension = Path.GetExtension(e.FullPath).ToLower();
                
                if (suspiciousExtensions.Contains(extension) && e.ChangeType == WatcherChangeTypes.Created)
                {
                    monitoringEvent.Severity = "Warning";
                    monitoringEvent.RequiresParentNotification = true;
                    monitoringEvent.Description = $"Suspicious file created: {Path.GetFileName(e.FullPath)}";
                }

                _eventQueue.Add(monitoringEvent);
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error processing file system event");
            }
        }

        private void OnFileSystemRenamed(object sender, RenamedEventArgs e)
        {
            try
            {
                var monitoringEvent = new MonitoringEvent
                {
                    DeviceId = _config.DeviceId,
                    EventType = "FileRenamed",
                    Category = "FileSystem",
                    Description = $"File renamed: {Path.GetFileName(e.OldFullPath)} → {Path.GetFileName(e.FullPath)}",
                    Data = new Dictionary<string, object>
                    {
                        ["OldPath"] = e.OldFullPath,
                        ["NewPath"] = e.FullPath,
                        ["OldFileName"] = Path.GetFileName(e.OldFullPath),
                        ["NewFileName"] = Path.GetFileName(e.FullPath),
                        ["Directory"] = Path.GetDirectoryName(e.FullPath) ?? ""
                    },
                    Severity = "Info"
                };

                _eventQueue.Add(monitoringEvent);
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error processing file rename event");
            }
        }

        private void OnUsbDeviceEvent(object sender, EventArrivedEventArgs e)
        {
            try
            {
                var eventType = Convert.ToInt32(e.NewEvent["EventType"]);
                var driveName = e.NewEvent["DriveName"]?.ToString() ?? "Unknown";
                
                var action = eventType switch
                {
                    2 => "Connected",
                    3 => "Disconnected",
                    _ => "Unknown"
                };

                var monitoringEvent = new MonitoringEvent
                {
                    DeviceId = _config.DeviceId,
                    EventType = "UsbDeviceEvent",
                    Category = "Hardware",
                    Description = $"USB device {action.ToLower()}: {driveName}",
                    Data = new Dictionary<string, object>
                    {
                        ["Action"] = action,
                        ["DriveName"] = driveName,
                        ["EventType"] = eventType
                    },
                    Severity = "Info",
                    RequiresParentNotification = action == "Connected" // Notify when USB devices are connected
                };

                _eventQueue.Add(monitoringEvent);
                _logger.LogInformation($"USB device {action.ToLower()}: {driveName}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing USB device event");
            }
        }

        private void OnLogonEvent(object sender, EventArrivedEventArgs e)
        {
            try
            {
                var monitoringEvent = new MonitoringEvent
                {
                    DeviceId = _config.DeviceId,
                    EventType = "UserLogon",
                    Category = "Security",
                    Description = "User logged on to the system",
                    Data = new Dictionary<string, object>
                    {
                        ["UserName"] = Environment.UserName,
                        ["MachineName"] = Environment.MachineName,
                        ["LogonTime"] = DateTime.UtcNow
                    },
                    Severity = "Info",
                    RequiresParentNotification = true
                };

                _eventQueue.Add(monitoringEvent);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing logon event");
            }
        }

        private void OnShutdownEvent(object sender, EventArrivedEventArgs e)
        {
            try
            {
                var monitoringEvent = new MonitoringEvent
                {
                    DeviceId = _config.DeviceId,
                    EventType = "SystemShutdown",
                    Category = "System",
                    Description = "System shutdown initiated",
                    Data = new Dictionary<string, object>
                    {
                        ["UserName"] = Environment.UserName,
                        ["MachineName"] = Environment.MachineName,
                        ["ShutdownTime"] = DateTime.UtcNow
                    },
                    Severity = "Info",
                    RequiresParentNotification = true
                };

                _eventQueue.Add(monitoringEvent);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing shutdown event");
            }
        }

        private async void OnStatsTimerElapsed(object sender, ElapsedEventArgs e)
        {
            try
            {
                await FlushEventQueueAsync();
                await SendSystemStatisticsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error sending system statistics");
            }
        }

        private async Task SendSystemStatisticsAsync()
        {
            try
            {
                var stats = new
                {
                    CpuUsage = GetCpuUsage(),
                    MemoryUsage = GetMemoryUsage(),
                    DiskUsage = GetDiskUsage(),
                    UptimeMinutes = Environment.TickCount / (1000 * 60),
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                var statsEvent = new MonitoringEvent
                {
                    DeviceId = _config.DeviceId,
                    EventType = "SystemStatistics",
                    Category = "System",
                    Description = "System performance statistics",
                    Data = new Dictionary<string, object>
                    {
                        ["CpuUsage"] = stats.CpuUsage,
                        ["MemoryUsage"] = stats.MemoryUsage,
                        ["DiskUsage"] = stats.DiskUsage,
                        ["UptimeMinutes"] = stats.UptimeMinutes
                    },
                    Severity = "Info"
                };

                await _apiClient.SendEventAsync(statsEvent);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send system statistics");
            }
        }

        private double GetCpuUsage()
        {
            // This is a simplified CPU usage calculation
            // In a production environment, you'd want to use PerformanceCounter
            return 0.0; // Placeholder
        }

        private double GetMemoryUsage()
        {
            var totalMemory = GC.GetTotalMemory(false);
            var workingSet = Environment.WorkingSet;
            return (double)workingSet / (1024 * 1024); // MB
        }

        private Dictionary<string, long> GetDiskUsage()
        {
            var diskUsage = new Dictionary<string, long>();
            
            try
            {
                var drives = DriveInfo.GetDrives();
                foreach (var drive in drives)
                {
                    if (drive.IsReady)
                    {
                        diskUsage[drive.Name] = drive.AvailableFreeSpace;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting disk usage");
            }
            
            return diskUsage;
        }

        private async Task FlushEventQueueAsync()
        {
            if (_eventQueue.Count == 0) return;

            try
            {
                var events = _eventQueue.ToList();
                _eventQueue.Clear();
                
                await _apiClient.SendBulkEventsAsync(events);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send system events to server");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopFileSystemMonitoring();
                StopUsbDeviceMonitoring();
                StopSystemEventMonitoring();
                _statsTimer?.Dispose();
                _disposed = true;
            }
        }
    }
}
