# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
dist/
build/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Database
*.sqlite
*.db

# Uploads
uploads/
temp/

# Windows specific
clients/desktop/windows/*/bin/
clients/desktop/windows/*/obj/
clients/desktop/windows/.vs/
*.user
*.suo

# macOS specific  
clients/desktop/macos/Build/
clients/desktop/macos/DerivedData/

# Mobile specific
clients/mobile/android/.gradle/
clients/mobile/android/build/
clients/mobile/ios/build/
clients/mobile/ios/Pods/

# Testing
test-results/
playwright-report/

# Temporary files
*.tmp
*.temp
