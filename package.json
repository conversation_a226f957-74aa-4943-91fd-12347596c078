{"name": "watchtower", "version": "1.0.0", "description": "Comprehensive parental monitoring solution", "private": true, "workspaces": ["server", "web-dashboard", "clients/mobile"], "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:dashboard\"", "dev:server": "cd server && npm run dev", "dev:dashboard": "cd web-dashboard && npm run dev", "build": "npm run build:server && npm run build:dashboard", "build:server": "cd server && npm run build", "build:dashboard": "cd web-dashboard && npm run build", "test": "npm run test:server && npm run test:dashboard", "test:server": "cd server && npm test", "test:dashboard": "cd web-dashboard && npm test", "lint": "npm run lint:server && npm run lint:dashboard", "lint:server": "cd server && npm run lint", "lint:dashboard": "cd web-dashboard && npm run lint", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "setup": "npm install && npm run setup:server && npm run setup:dashboard", "setup:server": "cd server && npm install && npm run migrate", "setup:dashboard": "cd web-dashboard && npm install"}, "devDependencies": {"concurrently": "^8.2.0", "husky": "^8.0.0", "lint-staged": "^14.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}